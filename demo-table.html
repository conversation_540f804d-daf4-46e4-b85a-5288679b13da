<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>筛选列表页面 - 表格风格</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f0f2f5;
        }

        .container {
            padding: 16px;
            background-color: #f0f2f5;
            min-height: 100vh;
        }

        .page-title {
            font-size: 16px;
            font-weight: 500;
            color: #000000;
            margin-bottom: 16px;
        }

        .filter-bar {
            background: #fff;
            border-radius: 2px;
            padding: 16px;
            margin-bottom: 16px;
            border: 1px solid #d9d9d9;
            display: flex;
            gap: 12px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-input {
            padding: 6px 11px;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            font-size: 14px;
            height: 32px;
            box-sizing: border-box;
            min-width: 200px;
        }

        .filter-select {
            padding: 6px 11px;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            font-size: 14px;
            height: 32px;
            box-sizing: border-box;
            min-width: 120px;
        }

        .btn-primary {
            background: #1890ff;
            color: white;
            border: none;
            padding: 4px 15px;
            border-radius: 2px;
            cursor: pointer;
            font-size: 14px;
            height: 32px;
            line-height: 22px;
            transition: background-color 0.3s;
        }

        .btn-primary:hover {
            background: #40a9ff;
        }

        .btn-small {
            background: #1890ff;
            color: white;
            border: none;
            padding: 4px 15px;
            border-radius: 2px;
            cursor: pointer;
            font-size: 12px;
            height: 24px;
            line-height: 14px;
            transition: background-color 0.3s;
        }

        .btn-small:hover {
            background: #40a9ff;
        }

        .table-container {
            background: #fff;
            border-radius: 2px;
            border: 1px solid #d9d9d9;
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .table th {
            background: #fafafa;
            border-bottom: 1px solid #e8e8e8;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: #000000;
            font-size: 14px;
        }

        .table td {
            border-bottom: 1px solid #e8e8e8;
            padding: 12px 16px;
            color: #000000;
            vertical-align: top;
        }

        .table tr:hover {
            background: #fafafa;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .status-tag {
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 400;
            color: #fff;
            display: inline-block;
        }

        .status-processing { background-color: #1890ff; }
        .status-completed { background-color: #52c41a; }
        .status-waiting { background-color: #faad14; }
        .status-error { background-color: #ff4d4f; }

        .expandable-row {
            cursor: pointer;
        }

        .expand-icon {
            display: inline-block;
            margin-right: 8px;
            transition: transform 0.3s;
        }

        .expand-icon.expanded {
            transform: rotate(90deg);
        }

        .sub-table-container {
            background: #f9f9f9;
            border-top: 1px solid #e8e8e8;
            padding: 16px;
            display: none;
        }

        .sub-table-container.expanded {
            display: block;
        }

        .sub-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
            background: #fff;
            border: 1px solid #e8e8e8;
        }

        .sub-table th {
            background: #f5f5f5;
            border-bottom: 1px solid #e8e8e8;
            padding: 8px 12px;
            text-align: left;
            font-weight: 500;
            color: #333333;
            font-size: 13px;
        }

        .sub-table td {
            border-bottom: 1px solid #e8e8e8;
            padding: 8px 12px;
            color: #000000;
        }

        .sub-table tr:hover {
            background: #fafafa;
        }

        .sub-table tr:last-child td {
            border-bottom: none;
        }

        .stage-tag {
            padding: 2px 6px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 400;
            color: #fff;
            display: inline-block;
        }

        .stage-bubble { background-color: #1890ff; }
        .stage-dispatch { background-color: #52c41a; }
        .stage-waiting { background-color: #faad14; }

        .empty-value {
            color: #999999;
            font-style: italic;
        }

        .stage-section {
            margin-bottom: 16px;
        }

        .stage-title {
            font-size: 13px;
            font-weight: 500;
            color: #333333;
            margin-bottom: 8px;
            padding: 4px 0;
            border-bottom: 1px solid #e8e8e8;
        }

        @media (max-width: 768px) {
            .container {
                padding: 12px;
            }
            
            .filter-bar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-input, .filter-select {
                min-width: auto;
                width: 100%;
            }
            
            .table, .sub-table {
                font-size: 12px;
            }
            
            .table th, .table td {
                padding: 8px 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="page-title">筛选列表</h1>

        <!-- 筛选条件 -->
        <div class="filter-bar">
            <input type="text" class="filter-input" placeholder="请输入关键词搜索" id="searchInput">
            <select class="filter-select" id="statusFilter">
                <option value="all">全部状态</option>
                <option value="处理中">处理中</option>
                <option value="已完成">已完成</option>
                <option value="等待处理">等待处理</option>
                <option value="异常">异常</option>
            </select>
            <button class="btn-primary">查询</button>
        </div>

        <!-- 数据表格 -->
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th width="40%">标题</th>
                        <th width="25%">描述</th>
                        <th width="15%">创建时间</th>
                        <th width="10%">状态</th>
                        <th width="10%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="expandable-row" onclick="toggleRow(this)">
                        <td>
                            <span class="expand-icon">▶</span>
                            订单处理流程 #001
                        </td>
                        <td>客户订单号：ORD-2024-001，商品：智能手机</td>
                        <td>2024-01-15 09:30</td>
                        <td><span class="status-tag status-processing">处理中</span></td>
                        <td><button class="btn-small">详情</button></td>
                    </tr>
                    <tr class="sub-table-container">
                        <td colspan="5">
                            <div class="stage-section">
                                <div class="stage-title">冒泡 (1 条)</div>
                                <table class="sub-table">
                                    <thead>
                                        <tr>
                                            <th>TraceID</th>
                                            <th>阶段</th>
                                            <th>时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>trace_001_bubble</td>
                                            <td><span class="stage-tag stage-bubble">冒泡</span></td>
                                            <td>2024-01-15 09:30:15</td>
                                            <td><button class="btn-small" onclick="window.open('https://example.com/trace/001/bubble', '_blank')">查看</button></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div class="stage-section">
                                <div class="stage-title">发单 (1 条)</div>
                                <table class="sub-table">
                                    <thead>
                                        <tr>
                                            <th>TraceID</th>
                                            <th>阶段</th>
                                            <th>时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>trace_001_dispatch</td>
                                            <td><span class="stage-tag stage-dispatch">发单</span></td>
                                            <td>2024-01-15 09:32:20</td>
                                            <td><button class="btn-small" onclick="window.open('https://example.com/trace/001/dispatch', '_blank')">查看</button></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div class="stage-section">
                                <div class="stage-title">等应答 (3 条)</div>
                                <table class="sub-table">
                                    <thead>
                                        <tr>
                                            <th>TraceID</th>
                                            <th>阶段</th>
                                            <th>时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>trace_001_wait_1</td>
                                            <td><span class="stage-tag stage-waiting">等应答</span></td>
                                            <td>2024-01-15 09:35:10</td>
                                            <td><button class="btn-small" onclick="window.open('https://example.com/trace/001/wait/1', '_blank')">查看</button></td>
                                        </tr>
                                        <tr>
                                            <td>trace_001_wait_2</td>
                                            <td><span class="stage-tag stage-waiting">等应答</span></td>
                                            <td>2024-01-15 09:38:45</td>
                                            <td><button class="btn-small" onclick="window.open('https://example.com/trace/001/wait/2', '_blank')">查看</button></td>
                                        </tr>
                                        <tr>
                                            <td><span class="empty-value">暂无数据</span></td>
                                            <td><span class="stage-tag stage-waiting">等应答</span></td>
                                            <td>2024-01-15 09:42:30</td>
                                            <td><button class="btn-small" onclick="window.open('https://example.com/trace/001/wait/3', '_blank')">查看</button></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    </tr>

                    <tr class="expandable-row" onclick="toggleRow(this)">
                        <td>
                            <span class="expand-icon">▶</span>
                            订单处理流程 #002
                        </td>
                        <td>客户订单号：ORD-2024-002，商品：笔记本电脑</td>
                        <td>2024-01-15 10:15</td>
                        <td><span class="status-tag status-completed">已完成</span></td>
                        <td><button class="btn-small">详情</button></td>
                    </tr>
                    <tr class="sub-table-container">
                        <td colspan="5">
                            <p style="text-align: center; color: #666666; padding: 16px; font-size: 13px;">点击行展开查看子列表内容</p>
                        </td>
                    </tr>

                    <tr class="expandable-row" onclick="toggleRow(this)">
                        <td>
                            <span class="expand-icon">▶</span>
                            订单处理流程 #003
                        </td>
                        <td>客户订单号：ORD-2024-003，商品：平板电脑</td>
                        <td>2024-01-15 11:00</td>
                        <td><span class="status-tag status-waiting">等待处理</span></td>
                        <td><button class="btn-small">详情</button></td>
                    </tr>
                    <tr class="sub-table-container">
                        <td colspan="5">
                            <p style="text-align: center; color: #666666; padding: 16px; font-size: 13px;">点击行展开查看子列表内容</p>
                        </td>
                    </tr>

                    <tr class="expandable-row" onclick="toggleRow(this)">
                        <td>
                            <span class="expand-icon">▶</span>
                            订单处理流程 #004
                        </td>
                        <td>客户订单号：ORD-2024-004，商品：智能手表</td>
                        <td>2024-01-15 14:20</td>
                        <td><span class="status-tag status-processing">处理中</span></td>
                        <td><button class="btn-small">详情</button></td>
                    </tr>
                    <tr class="sub-table-container">
                        <td colspan="5">
                            <p style="text-align: center; color: #666666; padding: 16px; font-size: 13px;">点击行展开查看子列表内容</p>
                        </td>
                    </tr>

                    <tr class="expandable-row" onclick="toggleRow(this)">
                        <td>
                            <span class="expand-icon">▶</span>
                            订单处理流程 #005
                        </td>
                        <td>客户订单号：ORD-2024-005，商品：蓝牙耳机</td>
                        <td>2024-01-15 15:45</td>
                        <td><span class="status-tag status-error">异常</span></td>
                        <td><button class="btn-small">详情</button></td>
                    </tr>
                    <tr class="sub-table-container">
                        <td colspan="5">
                            <p style="text-align: center; color: #666666; padding: 16px; font-size: 13px;">点击行展开查看子列表内容</p>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        function toggleRow(row) {
            const subRow = row.nextElementSibling;
            const expandIcon = row.querySelector('.expand-icon');
            const isExpanded = subRow.classList.contains('expanded');
            
            if (isExpanded) {
                subRow.classList.remove('expanded');
                expandIcon.classList.remove('expanded');
                expandIcon.textContent = '▶';
            } else {
                subRow.classList.add('expanded');
                expandIcon.classList.add('expanded');
                expandIcon.textContent = '▼';
            }
        }

        // 搜索功能
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const rows = document.querySelectorAll('.expandable-row');
            
            rows.forEach(row => {
                const title = row.cells[0].textContent.toLowerCase();
                const description = row.cells[1].textContent.toLowerCase();
                const subRow = row.nextElementSibling;
                
                if (title.includes(searchTerm) || description.includes(searchTerm)) {
                    row.style.display = '';
                    subRow.style.display = '';
                } else {
                    row.style.display = 'none';
                    subRow.style.display = 'none';
                }
            });
        });

        // 状态筛选功能
        document.getElementById('statusFilter').addEventListener('change', function(e) {
            const selectedStatus = e.target.value;
            const rows = document.querySelectorAll('.expandable-row');
            
            rows.forEach(row => {
                const statusElement = row.querySelector('.status-tag');
                const itemStatus = statusElement.textContent.trim();
                const subRow = row.nextElementSibling;
                
                if (selectedStatus === 'all' || itemStatus === selectedStatus) {
                    row.style.display = '';
                    subRow.style.display = '';
                } else {
                    row.style.display = 'none';
                    subRow.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
