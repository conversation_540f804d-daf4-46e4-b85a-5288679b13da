{"name": "untitled", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "webpack serve --mode development", "build": "webpack --mode production", "test": "echo \"Error: no test specified\" && exit 1"}, "private": true, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@ant-design/icons": "^6.0.0", "antd": "^5.26.7", "react": "^19.1.1", "react-dom": "^19.1.1"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "babel-loader": "^10.0.0", "css-loader": "^7.1.2", "html-webpack-plugin": "^5.6.3", "style-loader": "^4.0.0", "webpack": "^5.101.0", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}}