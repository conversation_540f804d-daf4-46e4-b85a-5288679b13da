// Mock数据 - 筛选列表数据
export const mockFilterListData = [
  {
    id: 1,
    title: "订单处理流程 #001",
    description: "客户订单号：ORD-2024-001，商品：智能手机",
    createTime: "2024-01-15 09:30:00",
    status: "处理中",
    subItems: [
      {
        id: 101,
        traceid: "trace_001_bubble",
        stage: "冒泡",
        time: "2024-01-15 09:30:15",
        viewUrl: "https://example.com/trace/001/bubble"
      },
      {
        id: 102,
        traceid: "trace_001_dispatch",
        stage: "发单",
        time: "2024-01-15 09:32:20",
        viewUrl: "https://example.com/trace/001/dispatch"
      },
      {
        id: 103,
        traceid: "trace_001_wait_1",
        stage: "等应答",
        time: "2024-01-15 09:35:10",
        viewUrl: "https://example.com/trace/001/wait/1"
      },
      {
        id: 104,
        traceid: "trace_001_wait_2",
        stage: "等应答",
        time: "2024-01-15 09:38:45",
        viewUrl: "https://example.com/trace/001/wait/2"
      },
      {
        id: 105,
        traceid: "",
        stage: "等应答",
        time: "2024-01-15 09:42:30",
        viewUrl: "https://example.com/trace/001/wait/3"
      }
    ]
  },
  {
    id: 2,
    title: "订单处理流程 #002",
    description: "客户订单号：ORD-2024-002，商品：笔记本电脑",
    createTime: "2024-01-15 10:15:00",
    status: "已完成",
    subItems: [
      {
        id: 201,
        traceid: "trace_002_bubble",
        stage: "冒泡",
        time: "2024-01-15 10:15:30",
        viewUrl: "https://example.com/trace/002/bubble"
      },
      {
        id: 202,
        traceid: "trace_002_dispatch",
        stage: "发单",
        time: "2024-01-15 10:18:15",
        viewUrl: "https://example.com/trace/002/dispatch"
      },
      {
        id: 203,
        traceid: "trace_002_wait_1",
        stage: "等应答",
        time: "2024-01-15 10:20:45",
        viewUrl: "https://example.com/trace/002/wait/1"
      }
    ]
  },
  {
    id: 3,
    title: "订单处理流程 #003",
    description: "客户订单号：ORD-2024-003，商品：平板电脑",
    createTime: "2024-01-15 11:00:00",
    status: "等待处理",
    subItems: [
      {
        id: 301,
        traceid: "trace_003_bubble",
        stage: "冒泡",
        time: "2024-01-15 11:00:20",
        viewUrl: "https://example.com/trace/003/bubble"
      },
      {
        id: 302,
        traceid: "trace_003_dispatch",
        stage: "发单",
        time: "2024-01-15 11:03:10",
        viewUrl: "https://example.com/trace/003/dispatch"
      },
      {
        id: 303,
        traceid: "trace_003_wait_1",
        stage: "等应答",
        time: "2024-01-15 11:05:30",
        viewUrl: "https://example.com/trace/003/wait/1"
      },
      {
        id: 304,
        traceid: "trace_003_wait_2",
        stage: "等应答",
        time: "2024-01-15 11:08:15",
        viewUrl: "https://example.com/trace/003/wait/2"
      },
      {
        id: 305,
        traceid: "trace_003_wait_3",
        stage: "等应答",
        time: "2024-01-15 11:12:00",
        viewUrl: "https://example.com/trace/003/wait/3"
      },
      {
        id: 306,
        traceid: "",
        stage: "等应答",
        time: "2024-01-15 11:15:45",
        viewUrl: "https://example.com/trace/003/wait/4"
      }
    ]
  },
  {
    id: 4,
    title: "订单处理流程 #004",
    description: "客户订单号：ORD-2024-004，商品：智能手表",
    createTime: "2024-01-15 14:20:00",
    status: "处理中",
    subItems: [
      {
        id: 401,
        traceid: "trace_004_bubble",
        stage: "冒泡",
        time: "2024-01-15 14:20:25",
        viewUrl: "https://example.com/trace/004/bubble"
      },
      {
        id: 402,
        traceid: "trace_004_dispatch",
        stage: "发单",
        time: "2024-01-15 14:23:40",
        viewUrl: "https://example.com/trace/004/dispatch"
      },
      {
        id: 403,
        traceid: "trace_004_wait_1",
        stage: "等应答",
        time: "2024-01-15 14:26:10",
        viewUrl: "https://example.com/trace/004/wait/1"
      },
      {
        id: 404,
        traceid: "",
        stage: "等应答",
        time: "2024-01-15 14:30:55",
        viewUrl: "https://example.com/trace/004/wait/2"
      }
    ]
  },
  {
    id: 5,
    title: "订单处理流程 #005",
    description: "客户订单号：ORD-2024-005，商品：蓝牙耳机",
    createTime: "2024-01-15 15:45:00",
    status: "异常",
    subItems: [
      {
        id: 501,
        traceid: "trace_005_bubble",
        stage: "冒泡",
        time: "2024-01-15 15:45:15",
        viewUrl: "https://example.com/trace/005/bubble"
      },
      {
        id: 502,
        traceid: "trace_005_dispatch",
        stage: "发单",
        time: "2024-01-15 15:48:30",
        viewUrl: "https://example.com/trace/005/dispatch"
      }
    ]
  }
];

// 阶段枚举
export const STAGE_TYPES = {
  BUBBLE: "冒泡",
  DISPATCH: "发单", 
  WAITING: "等应答"
};

// 阶段颜色配置
export const STAGE_COLORS = {
  [STAGE_TYPES.BUBBLE]: "#1890ff",
  [STAGE_TYPES.DISPATCH]: "#52c41a", 
  [STAGE_TYPES.WAITING]: "#faad14"
};

// 状态颜色配置
export const STATUS_COLORS = {
  "处理中": "#1890ff",
  "已完成": "#52c41a",
  "等待处理": "#faad14",
  "异常": "#ff4d4f"
};
