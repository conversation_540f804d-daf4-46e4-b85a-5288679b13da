/* 筛选列表样式 */
.filter-list-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.filter-list-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 24px;
  text-align: center;
}

.filter-list-wrapper {
  max-width: 1200px;
  margin: 0 auto;
}

/* 列表项样式 */
.filter-list-item {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.filter-list-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.filter-list-item-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-list-item-info {
  flex: 1;
}

.filter-list-item-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
}

.filter-list-item-description {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.filter-list-item-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #bfbfbf;
}

.filter-list-item-status {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: #fff;
}

.filter-list-item-actions {
  margin-left: 16px;
}

/* 子列表样式 */
.sub-list-container {
  padding: 0 24px 20px;
  background-color: #fafafa;
}

.sub-list-header {
  padding: 16px 0 12px;
  font-size: 14px;
  font-weight: 600;
  color: #595959;
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 12px;
}

.sub-list-item {
  background: #fff;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 8px;
  border: 1px solid #f0f0f0;
  transition: all 0.2s ease;
}

.sub-list-item:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.sub-list-item:last-child {
  margin-bottom: 0;
}

.sub-list-item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sub-list-item-info {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 120px 180px;
  gap: 16px;
  align-items: center;
}

.sub-list-item-field {
  display: flex;
  flex-direction: column;
}

.sub-list-item-label {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.sub-list-item-value {
  font-size: 14px;
  color: #262626;
  word-break: break-all;
}

.sub-list-item-value.empty {
  color: #bfbfbf;
  font-style: italic;
}

.sub-list-item-stage {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: #fff;
}

.sub-list-item-time {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.sub-list-item-actions {
  margin-left: 16px;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #8c8c8c;
}

.empty-state-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state-text {
  font-size: 16px;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-list-container {
    padding: 16px;
  }
  
  .filter-list-item-header {
    padding: 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .filter-list-item-actions {
    margin-left: 0;
    align-self: flex-end;
  }
  
  .sub-list-container {
    padding: 0 16px 16px;
  }
  
  .sub-list-item-info {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .sub-list-item-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .sub-list-item-actions {
    margin-left: 0;
    align-self: flex-end;
  }
}

/* 动画效果 */
.filter-list-item-enter {
  opacity: 0;
  transform: translateY(20px);
}

.filter-list-item-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.3s ease;
}

.sub-list-enter {
  opacity: 0;
  max-height: 0;
  overflow: hidden;
}

.sub-list-enter-active {
  opacity: 1;
  max-height: 1000px;
  transition: all 0.3s ease;
}

.sub-list-exit {
  opacity: 1;
  max-height: 1000px;
}

.sub-list-exit-active {
  opacity: 0;
  max-height: 0;
  transition: all 0.3s ease;
}
