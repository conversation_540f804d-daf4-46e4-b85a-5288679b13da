import React, { useState, useEffect } from 'react';
import { Spin, Empty, Input, Select, Space, Card, Statistic, Row, Col } from 'antd';
import { SearchOutlined, FilterOutlined, UnorderedListOutlined } from '@ant-design/icons';
import FilterListItem from './FilterListItem';
import { mockFilterListData, STATUS_COLORS } from '../data/mockData';
import '../styles/FilterList.css';

const { Search } = Input;
const { Option } = Select;

const FilterList = () => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // 模拟数据加载
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
      setData(mockFilterListData);
      setFilteredData(mockFilterListData);
      setLoading(false);
    };

    loadData();
  }, []);

  // 搜索和筛选逻辑
  useEffect(() => {
    let filtered = [...data];

    // 文本搜索
    if (searchText) {
      filtered = filtered.filter(item => 
        item.title.toLowerCase().includes(searchText.toLowerCase()) ||
        item.description.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    // 状态筛选
    if (statusFilter !== 'all') {
      filtered = filtered.filter(item => item.status === statusFilter);
    }

    setFilteredData(filtered);
  }, [data, searchText, statusFilter]);

  const handleSearch = (value) => {
    setSearchText(value);
  };

  const handleStatusFilterChange = (value) => {
    setStatusFilter(value);
  };

  // 获取统计数据
  const getStatistics = () => {
    const total = data.length;
    const statusCounts = data.reduce((acc, item) => {
      acc[item.status] = (acc[item.status] || 0) + 1;
      return acc;
    }, {});

    const totalSubItems = data.reduce((acc, item) => acc + (item.subItems?.length || 0), 0);

    return { total, statusCounts, totalSubItems };
  };

  const statistics = getStatistics();

  if (loading) {
    return (
      <div className="loading-container">
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  return (
    <div className="filter-list-container">
      <div className="filter-list-wrapper">
        <h1 className="filter-list-title">
          <UnorderedListOutlined style={{ marginRight: 12 }} />
          筛选列表管理
        </h1>

        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic 
                title="总数量" 
                value={statistics.total} 
                prefix={<UnorderedListOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic 
                title="子项目总数" 
                value={statistics.totalSubItems}
                prefix={<FilterOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic 
                title="处理中" 
                value={statistics.statusCounts['处理中'] || 0}
                valueStyle={{ color: STATUS_COLORS['处理中'] }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic 
                title="已完成" 
                value={statistics.statusCounts['已完成'] || 0}
                valueStyle={{ color: STATUS_COLORS['已完成'] }}
              />
            </Card>
          </Col>
        </Row>

        {/* 搜索和筛选区域 */}
        <Card style={{ marginBottom: 24 }}>
          <Space size="middle" wrap style={{ width: '100%' }}>
            <Search
              placeholder="搜索标题或描述..."
              allowClear
              enterButton={<SearchOutlined />}
              size="large"
              style={{ width: 300 }}
              onSearch={handleSearch}
              onChange={(e) => !e.target.value && setSearchText('')}
            />
            
            <Select
              placeholder="筛选状态"
              size="large"
              style={{ width: 150 }}
              value={statusFilter}
              onChange={handleStatusFilterChange}
            >
              <Option value="all">全部状态</Option>
              <Option value="处理中">处理中</Option>
              <Option value="已完成">已完成</Option>
              <Option value="等待处理">等待处理</Option>
              <Option value="异常">异常</Option>
            </Select>

            <span style={{ color: '#8c8c8c', fontSize: 14 }}>
              共找到 {filteredData.length} 条记录
            </span>
          </Space>
        </Card>

        {/* 列表内容 */}
        {filteredData.length === 0 ? (
          <div className="empty-state">
            <Empty 
              description={
                searchText || statusFilter !== 'all' 
                  ? "没有找到符合条件的数据" 
                  : "暂无数据"
              }
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </div>
        ) : (
          <div className="filter-list-items">
            {filteredData.map(item => (
              <FilterListItem key={item.id} item={item} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default FilterList;
