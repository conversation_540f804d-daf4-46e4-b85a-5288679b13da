import React from 'react';
import { Button } from 'antd';
import { EyeOutlined } from '@ant-design/icons';
import { STAGE_COLORS } from '../data/mockData';

const SubListItem = ({ item }) => {
  const handleViewClick = () => {
    // 在新标签页中打开指定页面
    window.open(item.viewUrl, '_blank', 'noopener,noreferrer');
  };

  const getStageColor = (stage) => {
    return STAGE_COLORS[stage] || '#d9d9d9';
  };

  const formatTime = (timeString) => {
    try {
      const date = new Date(timeString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (error) {
      return timeString;
    }
  };

  return (
    <div className="sub-list-item">
      <div className="sub-list-item-content">
        <div className="sub-list-item-info">
          {/* TraceID 字段 */}
          <div className="sub-list-item-field">
            <div className="sub-list-item-label">TraceID</div>
            <div className={`sub-list-item-value ${!item.traceid ? 'empty' : ''}`}>
              {item.traceid || '暂无数据'}
            </div>
          </div>

          {/* 阶段字段 */}
          <div className="sub-list-item-field">
            <div className="sub-list-item-label">阶段</div>
            <div className="sub-list-item-value">
              <span 
                className="sub-list-item-stage"
                style={{ backgroundColor: getStageColor(item.stage) }}
              >
                {item.stage}
              </span>
            </div>
          </div>

          {/* 时间字段 */}
          <div className="sub-list-item-field">
            <div className="sub-list-item-label">时间</div>
            <div className="sub-list-item-value sub-list-item-time">
              {formatTime(item.time)}
            </div>
          </div>
        </div>

        {/* 查看按钮 */}
        <div className="sub-list-item-actions">
          <Button 
            type="primary" 
            size="small" 
            icon={<EyeOutlined />}
            onClick={handleViewClick}
          >
            查看
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SubListItem;
