import React, { useState } from 'react';
import { Button, Collapse } from 'antd';
import { DownOutlined, RightOutlined, InfoCircleOutlined } from '@ant-design/icons';
import SubListItem from './SubListItem';
import { STATUS_COLORS, STAGE_TYPES } from '../data/mockData';

const { Panel } = Collapse;

const FilterListItem = ({ item }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleDetailClick = () => {
    setIsExpanded(!isExpanded);
  };

  const getStatusColor = (status) => {
    return STATUS_COLORS[status] || '#d9d9d9';
  };

  const formatTime = (timeString) => {
    try {
      const date = new Date(timeString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return timeString;
    }
  };

  // 按阶段分组子项目
  const groupSubItemsByStage = (subItems) => {
    const grouped = {
      [STAGE_TYPES.BUBBLE]: [],
      [STAGE_TYPES.DISPATCH]: [],
      [STAGE_TYPES.WAITING]: []
    };

    subItems.forEach(subItem => {
      if (grouped[subItem.stage]) {
        grouped[subItem.stage].push(subItem);
      }
    });

    return grouped;
  };

  const groupedSubItems = groupSubItemsByStage(item.subItems || []);

  const renderSubList = () => {
    if (!isExpanded || !item.subItems || item.subItems.length === 0) {
      return null;
    }

    return (
      <div className="sub-list-container">
        <div className="sub-list-header">
          <InfoCircleOutlined style={{ marginRight: 8 }} />
          详细流程信息 ({item.subItems.length} 条记录)
        </div>
        
        {/* 按阶段渲染子列表 */}
        {Object.entries(groupedSubItems).map(([stage, stageItems]) => {
          if (stageItems.length === 0) return null;
          
          return (
            <div key={stage} style={{ marginBottom: 16 }}>
              <div style={{ 
                fontSize: 13, 
                fontWeight: 600, 
                color: '#595959', 
                marginBottom: 8,
                padding: '4px 0'
              }}>
                {stage} ({stageItems.length} 条)
              </div>
              {stageItems.map(subItem => (
                <SubListItem key={subItem.id} item={subItem} />
              ))}
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="filter-list-item">
      <div className="filter-list-item-header">
        <div className="filter-list-item-info">
          <div className="filter-list-item-title">{item.title}</div>
          <div className="filter-list-item-description">{item.description}</div>
          <div className="filter-list-item-meta">
            <span>创建时间: {formatTime(item.createTime)}</span>
            <span>子项目: {item.subItems?.length || 0} 条</span>
          </div>
        </div>
        
        <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
          <span 
            className="filter-list-item-status"
            style={{ backgroundColor: getStatusColor(item.status) }}
          >
            {item.status}
          </span>
          
          <div className="filter-list-item-actions">
            <Button 
              type="primary"
              size="middle"
              icon={isExpanded ? <DownOutlined /> : <RightOutlined />}
              onClick={handleDetailClick}
            >
              详情
            </Button>
          </div>
        </div>
      </div>
      
      {renderSubList()}
    </div>
  );
};

export default FilterListItem;
