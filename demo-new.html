<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>筛选结果列表</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f0f2f5;
        }

        .container {
            padding: 16px;
            background-color: #f0f2f5;
            min-height: 100vh;
        }

        .page-title {
            font-size: 16px;
            font-weight: 500;
            color: #000000;
            margin-bottom: 16px;
        }

        .filter-bar {
            background: #fff;
            border-radius: 2px;
            padding: 16px;
            margin-bottom: 16px;
            border: 1px solid #d9d9d9;
            display: flex;
            gap: 12px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-input {
            padding: 6px 11px;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            font-size: 14px;
            height: 32px;
            box-sizing: border-box;
            min-width: 200px;
        }

        .filter-select {
            padding: 6px 11px;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            font-size: 14px;
            height: 32px;
            box-sizing: border-box;
            min-width: 120px;
        }

        .btn-primary {
            background: #1890ff;
            color: white;
            border: none;
            padding: 4px 15px;
            border-radius: 2px;
            cursor: pointer;
            font-size: 14px;
            height: 32px;
            line-height: 22px;
            transition: background-color 0.3s;
        }

        .btn-primary:hover {
            background: #40a9ff;
        }

        .btn-small {
            background: #1890ff;
            color: white;
            border: none;
            padding: 4px 15px;
            border-radius: 2px;
            cursor: pointer;
            font-size: 12px;
            height: 24px;
            line-height: 14px;
            transition: background-color 0.3s;
        }

        .btn-small:hover {
            background: #40a9ff;
        }

        .table-container {
            background: #fff;
            border-radius: 2px;
            border: 1px solid #d9d9d9;
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .table th {
            background: #fafafa;
            border-bottom: 1px solid #e8e8e8;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: #000000;
            font-size: 14px;
        }

        .table td {
            border-bottom: 1px solid #e8e8e8;
            padding: 12px 16px;
            color: #000000;
            vertical-align: top;
        }

        .table tr:hover {
            background: #fafafa;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .expandable-row {
            cursor: pointer;
        }

        .expand-icon {
            display: inline-block;
            margin-right: 8px;
            transition: transform 0.3s;
            font-size: 12px;
        }

        .expand-icon.expanded {
            transform: rotate(90deg);
        }

        .sub-table-container {
            background: #f9f9f9;
            border-top: 1px solid #e8e8e8;
            padding: 16px;
            display: none;
        }

        .sub-table-container.expanded {
            display: block;
        }

        .sub-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
            background: #fff;
            border: 1px solid #e8e8e8;
        }

        .sub-table th {
            background: #f5f5f5;
            border-bottom: 1px solid #e8e8e8;
            padding: 8px 12px;
            text-align: left;
            font-weight: 500;
            color: #333333;
            font-size: 13px;
        }

        .sub-table td {
            border-bottom: 1px solid #e8e8e8;
            padding: 8px 12px;
            color: #000000;
        }

        .sub-table tr:hover {
            background: #fafafa;
        }

        .sub-table tr:last-child td {
            border-bottom: none;
        }

        .stage-tag {
            padding: 2px 6px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 400;
            color: #fff;
            display: inline-block;
        }

        .stage-bubble { background-color: #1890ff; }
        .stage-dispatch { background-color: #52c41a; }
        .stage-waiting { background-color: #faad14; }

        .empty-value {
            color: #999999;
            font-style: italic;
        }

        @media (max-width: 768px) {
            .container {
                padding: 12px;
            }
            
            .filter-bar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-input, .filter-select {
                min-width: auto;
                width: 100%;
            }
            
            .table, .sub-table {
                font-size: 12px;
            }
            
            .table th, .table td {
                padding: 8px 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="page-title">筛选结果列表</h1>

        <!-- 筛选条件 -->
        <div class="filter-bar">
            <input type="text" class="filter-input" placeholder="请输入关键词搜索" id="searchInput">
            <select class="filter-select" id="cityFilter">
                <option value="all">全部城市</option>
                <option value="北京">北京</option>
                <option value="上海">上海</option>
                <option value="广州">广州</option>
                <option value="深圳">深圳</option>
            </select>
            <select class="filter-select" id="platformFilter">
                <option value="all">全部端</option>
                <option value="乘客端">乘客端</option>
                <option value="司机端">司机端</option>
            </select>
            <button class="btn-primary">查询</button>
        </div>

        <!-- 数据表格 -->
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th width="20%">冒泡时间</th>
                        <th width="15%">城市</th>
                        <th width="15%">端</th>
                        <th width="15%">版本</th>
                        <th width="20%">支付方式</th>
                        <th width="15%">展开</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="expandable-row" onclick="toggleRow(this)">
                        <td>2024-01-15 09:30:15</td>
                        <td>北京</td>
                        <td>乘客端</td>
                        <td>v8.2.1</td>
                        <td>微信支付</td>
                        <td>
                            <span class="expand-icon">▶</span>
                            <button class="btn-small">展开详情</button>
                        </td>
                    </tr>
                    <tr class="sub-table-container">
                        <td colspan="6">
                            <table class="sub-table">
                                <thead>
                                    <tr>
                                        <th width="30%">TraceId</th>
                                        <th width="25%">时间</th>
                                        <th width="20%">阶段</th>
                                        <th width="25%">查看</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>trace_001_bubble_20240115</td>
                                        <td>2024-01-15 09:30:15</td>
                                        <td><span class="stage-tag stage-bubble">冒泡</span></td>
                                        <td><button class="btn-small" onclick="openBubbleTrace('trace_001_bubble_20240115')">查看</button></td>
                                    </tr>
                                    <tr>
                                        <td>order_001_dispatch_20240115</td>
                                        <td>2024-01-15 09:32:20</td>
                                        <td><span class="stage-tag stage-dispatch">发单</span></td>
                                        <td><button class="btn-small" onclick="openOrderDiagnosis('order_001_dispatch_20240115')">查看</button></td>
                                    </tr>
                                    <tr>
                                        <td>trace_001_wait_001</td>
                                        <td>2024-01-15 09:35:10</td>
                                        <td><span class="stage-tag stage-waiting">等应答</span></td>
                                        <td><button class="btn-small" onclick="openWaitTrace('trace_001_wait_001')">查看</button></td>
                                    </tr>
                                    <tr>
                                        <td>trace_001_wait_002</td>
                                        <td>2024-01-15 09:38:45</td>
                                        <td><span class="stage-tag stage-waiting">等应答</span></td>
                                        <td><button class="btn-small" onclick="openWaitTrace('trace_001_wait_002')">查看</button></td>
                                    </tr>
                                    <tr>
                                        <td><span class="empty-value">暂无TraceId</span></td>
                                        <td>2024-01-15 09:42:30</td>
                                        <td><span class="stage-tag stage-waiting">等应答</span></td>
                                        <td><button class="btn-small" onclick="openWaitTrace('')">查看</button></td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>

                    <tr class="expandable-row" onclick="toggleRow(this)">
                        <td>2024-01-15 10:15:30</td>
                        <td>上海</td>
                        <td>司机端</td>
                        <td>v7.8.5</td>
                        <td>支付宝</td>
                        <td>
                            <span class="expand-icon">▶</span>
                            <button class="btn-small">展开详情</button>
                        </td>
                    </tr>
                    <tr class="sub-table-container">
                        <td colspan="6">
                            <table class="sub-table">
                                <thead>
                                    <tr>
                                        <th width="30%">TraceId</th>
                                        <th width="25%">时间</th>
                                        <th width="20%">阶段</th>
                                        <th width="25%">查看</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>trace_002_bubble_20240115</td>
                                        <td>2024-01-15 10:15:30</td>
                                        <td><span class="stage-tag stage-bubble">冒泡</span></td>
                                        <td><button class="btn-small" onclick="openBubbleTrace('trace_002_bubble_20240115')">查看</button></td>
                                    </tr>
                                    <tr>
                                        <td>order_002_dispatch_20240115</td>
                                        <td>2024-01-15 10:18:15</td>
                                        <td><span class="stage-tag stage-dispatch">发单</span></td>
                                        <td><button class="btn-small" onclick="openOrderDiagnosis('order_002_dispatch_20240115')">查看</button></td>
                                    </tr>
                                    <tr>
                                        <td>trace_002_wait_001</td>
                                        <td>2024-01-15 10:20:45</td>
                                        <td><span class="stage-tag stage-waiting">等应答</span></td>
                                        <td><button class="btn-small" onclick="openWaitTrace('trace_002_wait_001')">查看</button></td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>

                    <tr class="expandable-row" onclick="toggleRow(this)">
                        <td>2024-01-15 11:00:20</td>
                        <td>广州</td>
                        <td>乘客端</td>
                        <td>v8.1.9</td>
                        <td>银行卡</td>
                        <td>
                            <span class="expand-icon">▶</span>
                            <button class="btn-small">展开详情</button>
                        </td>
                    </tr>
                    <tr class="sub-table-container">
                        <td colspan="6">
                            <table class="sub-table">
                                <thead>
                                    <tr>
                                        <th width="30%">TraceId</th>
                                        <th width="25%">时间</th>
                                        <th width="20%">阶段</th>
                                        <th width="25%">查看</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>trace_003_bubble_20240115</td>
                                        <td>2024-01-15 11:00:20</td>
                                        <td><span class="stage-tag stage-bubble">冒泡</span></td>
                                        <td><button class="btn-small" onclick="openBubbleTrace('trace_003_bubble_20240115')">查看</button></td>
                                    </tr>
                                    <tr>
                                        <td>order_003_dispatch_20240115</td>
                                        <td>2024-01-15 11:03:10</td>
                                        <td><span class="stage-tag stage-dispatch">发单</span></td>
                                        <td><button class="btn-small" onclick="openOrderDiagnosis('order_003_dispatch_20240115')">查看</button></td>
                                    </tr>
                                    <tr>
                                        <td>trace_003_wait_001</td>
                                        <td>2024-01-15 11:05:30</td>
                                        <td><span class="stage-tag stage-waiting">等应答</span></td>
                                        <td><button class="btn-small" onclick="openWaitTrace('trace_003_wait_001')">查看</button></td>
                                    </tr>
                                    <tr>
                                        <td>trace_003_wait_002</td>
                                        <td>2024-01-15 11:08:15</td>
                                        <td><span class="stage-tag stage-waiting">等应答</span></td>
                                        <td><button class="btn-small" onclick="openWaitTrace('trace_003_wait_002')">查看</button></td>
                                    </tr>
                                    <tr>
                                        <td>trace_003_wait_003</td>
                                        <td>2024-01-15 11:12:00</td>
                                        <td><span class="stage-tag stage-waiting">等应答</span></td>
                                        <td><button class="btn-small" onclick="openWaitTrace('trace_003_wait_003')">查看</button></td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        function toggleRow(row) {
            const subRow = row.nextElementSibling;
            const expandIcon = row.querySelector('.expand-icon');
            const isExpanded = subRow.classList.contains('expanded');
            
            if (isExpanded) {
                subRow.classList.remove('expanded');
                expandIcon.classList.remove('expanded');
                expandIcon.textContent = '▶';
            } else {
                subRow.classList.add('expanded');
                expandIcon.classList.add('expanded');
                expandIcon.textContent = '▼';
            }
        }

        // 冒泡阶段查看功能
        function openBubbleTrace(traceId) {
            if (traceId) {
                const url = `https://bubble-trace.didi.cn/query?traceId=${traceId}`;
                window.open(url, '_blank');
                console.log(`打开冒泡TraceId查询页面: ${url}`);
            } else {
                alert('TraceId为空，无法查询');
            }
        }

        // 发单阶段查看功能 - 乘客诊断
        function openOrderDiagnosis(orderId) {
            if (orderId) {
                const url = `https://passenger-diagnosis.didi.cn/order?orderId=${orderId}`;
                window.open(url, '_blank');
                console.log(`打开乘客诊断订单页面: ${url}`);
            } else {
                alert('订单ID为空，无法查询');
            }
        }

        // 等应答阶段查看功能
        function openWaitTrace(traceId) {
            if (traceId) {
                const url = `https://wait-trace.didi.cn/query?traceId=${traceId}`;
                window.open(url, '_blank');
                console.log(`打开等应答TraceId查询页面: ${url}`);
            } else {
                alert('TraceId为空，无法查询');
            }
        }

        // 搜索功能
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const rows = document.querySelectorAll('.expandable-row');
            
            rows.forEach(row => {
                const rowText = row.textContent.toLowerCase();
                const subRow = row.nextElementSibling;
                
                if (rowText.includes(searchTerm)) {
                    row.style.display = '';
                    subRow.style.display = '';
                } else {
                    row.style.display = 'none';
                    subRow.style.display = 'none';
                }
            });
        });

        // 城市筛选功能
        document.getElementById('cityFilter').addEventListener('change', function(e) {
            const selectedCity = e.target.value;
            const rows = document.querySelectorAll('.expandable-row');
            
            rows.forEach(row => {
                const cityCell = row.cells[1].textContent.trim();
                const subRow = row.nextElementSibling;
                
                if (selectedCity === 'all' || cityCell === selectedCity) {
                    row.style.display = '';
                    subRow.style.display = '';
                } else {
                    row.style.display = 'none';
                    subRow.style.display = 'none';
                }
            });
        });

        // 端筛选功能
        document.getElementById('platformFilter').addEventListener('change', function(e) {
            const selectedPlatform = e.target.value;
            const rows = document.querySelectorAll('.expandable-row');
            
            rows.forEach(row => {
                const platformCell = row.cells[2].textContent.trim();
                const subRow = row.nextElementSibling;
                
                if (selectedPlatform === 'all' || platformCell === selectedPlatform) {
                    row.style.display = '';
                    subRow.style.display = '';
                } else {
                    row.style.display = 'none';
                    subRow.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
