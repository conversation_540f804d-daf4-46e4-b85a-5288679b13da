<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>筛选列表页面演示</title>
    <link rel="stylesheet" href="https://unpkg.com/antd@5.26.7/dist/reset.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f5f5f5;
        }

        .filter-list-container {
            padding: 24px;
            background-color: #f5f5f5;
            min-height: 100vh;
        }

        .filter-list-title {
            font-size: 24px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 24px;
            text-align: center;
        }

        .filter-list-wrapper {
            max-width: 1200px;
            margin: 0 auto;
        }

        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            text-align: center;
        }

        .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #1890ff;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            color: #8c8c8c;
        }

        .search-bar {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            display: flex;
            gap: 16px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 1;
            min-width: 300px;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            min-width: 150px;
        }

        .filter-list-item {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            margin-bottom: 16px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .filter-list-item:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }

        .filter-list-item-header {
            padding: 20px 24px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .filter-list-item-info {
            flex: 1;
        }

        .filter-list-item-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
        }

        .filter-list-item-description {
            font-size: 14px;
            color: #8c8c8c;
            margin-bottom: 8px;
        }

        .filter-list-item-meta {
            display: flex;
            gap: 16px;
            font-size: 12px;
            color: #bfbfbf;
        }

        .filter-list-item-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            color: #fff;
            margin-right: 12px;
        }

        .status-processing { background-color: #1890ff; }
        .status-completed { background-color: #52c41a; }
        .status-waiting { background-color: #faad14; }
        .status-error { background-color: #ff4d4f; }

        .detail-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .detail-btn:hover {
            background: #40a9ff;
        }

        .sub-list-container {
            padding: 0 24px 20px;
            background-color: #fafafa;
            display: none;
        }

        .sub-list-container.expanded {
            display: block;
        }

        .sub-list-header {
            padding: 16px 0 12px;
            font-size: 14px;
            font-weight: 600;
            color: #595959;
            border-bottom: 1px solid #e8e8e8;
            margin-bottom: 12px;
        }

        .sub-list-item {
            background: #fff;
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 8px;
            border: 1px solid #f0f0f0;
            transition: all 0.2s ease;
        }

        .sub-list-item:hover {
            border-color: #d9d9d9;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
        }

        .sub-list-item-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sub-list-item-info {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 120px 180px;
            gap: 16px;
            align-items: center;
        }

        .sub-list-item-field {
            display: flex;
            flex-direction: column;
        }

        .sub-list-item-label {
            font-size: 12px;
            color: #8c8c8c;
            margin-bottom: 4px;
        }

        .sub-list-item-value {
            font-size: 14px;
            color: #262626;
            word-break: break-all;
        }

        .sub-list-item-value.empty {
            color: #bfbfbf;
            font-style: italic;
        }

        .sub-list-item-stage {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            color: #fff;
        }

        .stage-bubble { background-color: #1890ff; }
        .stage-dispatch { background-color: #52c41a; }
        .stage-waiting { background-color: #faad14; }

        .sub-list-item-time {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
        }

        .view-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 4px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.3s;
        }

        .view-btn:hover {
            background: #40a9ff;
        }

        @media (max-width: 768px) {
            .filter-list-container {
                padding: 16px;
            }
            
            .filter-list-item-header {
                padding: 16px;
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }
            
            .sub-list-item-info {
                grid-template-columns: 1fr;
                gap: 12px;
            }
            
            .sub-list-item-content {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="filter-list-container">
        <div class="filter-list-wrapper">
            <h1 class="filter-list-title">📋 筛选列表管理</h1>

            <!-- 统计卡片 -->
            <div class="stats-row">
                <div class="stat-card">
                    <div class="stat-value">5</div>
                    <div class="stat-label">总数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">18</div>
                    <div class="stat-label">子项目总数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">2</div>
                    <div class="stat-label">处理中</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">1</div>
                    <div class="stat-label">已完成</div>
                </div>
            </div>

            <!-- 搜索和筛选区域 -->
            <div class="search-bar">
                <input type="text" class="search-input" placeholder="搜索标题或描述..." id="searchInput">
                <select class="filter-select" id="statusFilter">
                    <option value="all">全部状态</option>
                    <option value="处理中">处理中</option>
                    <option value="已完成">已完成</option>
                    <option value="等待处理">等待处理</option>
                    <option value="异常">异常</option>
                </select>
                <span style="color: #8c8c8c; font-size: 14px;">共找到 5 条记录</span>
            </div>

            <!-- 列表项 -->
            <div class="filter-list-item">
                <div class="filter-list-item-header">
                    <div class="filter-list-item-info">
                        <div class="filter-list-item-title">订单处理流程 #001</div>
                        <div class="filter-list-item-description">客户订单号：ORD-2024-001，商品：智能手机</div>
                        <div class="filter-list-item-meta">
                            <span>创建时间: 2024-01-15 09:30:00</span>
                            <span>子项目: 5 条</span>
                        </div>
                    </div>
                    
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <span class="filter-list-item-status status-processing">处理中</span>
                        <button class="detail-btn" onclick="toggleSubList(this)">详情 ▶</button>
                    </div>
                </div>
                
                <div class="sub-list-container">
                    <div class="sub-list-header">
                        ℹ️ 详细流程信息 (5 条记录)
                    </div>
                    
                    <div style="margin-bottom: 16px;">
                        <div style="font-size: 13px; font-weight: 600; color: #595959; margin-bottom: 8px; padding: 4px 0;">
                            冒泡 (1 条)
                        </div>
                        <div class="sub-list-item">
                            <div class="sub-list-item-content">
                                <div class="sub-list-item-info">
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">TraceID</div>
                                        <div class="sub-list-item-value">trace_001_bubble</div>
                                    </div>
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">阶段</div>
                                        <div class="sub-list-item-value">
                                            <span class="sub-list-item-stage stage-bubble">冒泡</span>
                                        </div>
                                    </div>
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">时间</div>
                                        <div class="sub-list-item-value sub-list-item-time">2024-01-15 09:30:15</div>
                                    </div>
                                </div>
                                <button class="view-btn" onclick="window.open('https://example.com/trace/001/bubble', '_blank')">👁️ 查看</button>
                            </div>
                        </div>
                    </div>

                    <div style="margin-bottom: 16px;">
                        <div style="font-size: 13px; font-weight: 600; color: #595959; margin-bottom: 8px; padding: 4px 0;">
                            发单 (1 条)
                        </div>
                        <div class="sub-list-item">
                            <div class="sub-list-item-content">
                                <div class="sub-list-item-info">
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">TraceID</div>
                                        <div class="sub-list-item-value">trace_001_dispatch</div>
                                    </div>
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">阶段</div>
                                        <div class="sub-list-item-value">
                                            <span class="sub-list-item-stage stage-dispatch">发单</span>
                                        </div>
                                    </div>
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">时间</div>
                                        <div class="sub-list-item-value sub-list-item-time">2024-01-15 09:32:20</div>
                                    </div>
                                </div>
                                <button class="view-btn" onclick="window.open('https://example.com/trace/001/dispatch', '_blank')">👁️ 查看</button>
                            </div>
                        </div>
                    </div>

                    <div style="margin-bottom: 16px;">
                        <div style="font-size: 13px; font-weight: 600; color: #595959; margin-bottom: 8px; padding: 4px 0;">
                            等应答 (3 条)
                        </div>
                        <div class="sub-list-item">
                            <div class="sub-list-item-content">
                                <div class="sub-list-item-info">
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">TraceID</div>
                                        <div class="sub-list-item-value">trace_001_wait_1</div>
                                    </div>
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">阶段</div>
                                        <div class="sub-list-item-value">
                                            <span class="sub-list-item-stage stage-waiting">等应答</span>
                                        </div>
                                    </div>
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">时间</div>
                                        <div class="sub-list-item-value sub-list-item-time">2024-01-15 09:35:10</div>
                                    </div>
                                </div>
                                <button class="view-btn" onclick="window.open('https://example.com/trace/001/wait/1', '_blank')">👁️ 查看</button>
                            </div>
                        </div>
                        <div class="sub-list-item">
                            <div class="sub-list-item-content">
                                <div class="sub-list-item-info">
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">TraceID</div>
                                        <div class="sub-list-item-value">trace_001_wait_2</div>
                                    </div>
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">阶段</div>
                                        <div class="sub-list-item-value">
                                            <span class="sub-list-item-stage stage-waiting">等应答</span>
                                        </div>
                                    </div>
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">时间</div>
                                        <div class="sub-list-item-value sub-list-item-time">2024-01-15 09:38:45</div>
                                    </div>
                                </div>
                                <button class="view-btn" onclick="window.open('https://example.com/trace/001/wait/2', '_blank')">👁️ 查看</button>
                            </div>
                        </div>
                        <div class="sub-list-item">
                            <div class="sub-list-item-content">
                                <div class="sub-list-item-info">
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">TraceID</div>
                                        <div class="sub-list-item-value empty">暂无数据</div>
                                    </div>
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">阶段</div>
                                        <div class="sub-list-item-value">
                                            <span class="sub-list-item-stage stage-waiting">等应答</span>
                                        </div>
                                    </div>
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">时间</div>
                                        <div class="sub-list-item-value sub-list-item-time">2024-01-15 09:42:30</div>
                                    </div>
                                </div>
                                <button class="view-btn" onclick="window.open('https://example.com/trace/001/wait/3', '_blank')">👁️ 查看</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 更多列表项示例 -->
            <div class="filter-list-item">
                <div class="filter-list-item-header">
                    <div class="filter-list-item-info">
                        <div class="filter-list-item-title">订单处理流程 #002</div>
                        <div class="filter-list-item-description">客户订单号：ORD-2024-002，商品：笔记本电脑</div>
                        <div class="filter-list-item-meta">
                            <span>创建时间: 2024-01-15 10:15:00</span>
                            <span>子项目: 3 条</span>
                        </div>
                    </div>
                    
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <span class="filter-list-item-status status-completed">已完成</span>
                        <button class="detail-btn" onclick="toggleSubList(this)">详情 ▶</button>
                    </div>
                </div>
                
                <div class="sub-list-container">
                    <div class="sub-list-header">
                        ℹ️ 详细流程信息 (3 条记录)
                    </div>
                    <p style="text-align: center; color: #8c8c8c; padding: 20px;">点击详情按钮查看子列表内容</p>
                </div>
            </div>

            <div class="filter-list-item">
                <div class="filter-list-item-header">
                    <div class="filter-list-item-info">
                        <div class="filter-list-item-title">订单处理流程 #003</div>
                        <div class="filter-list-item-description">客户订单号：ORD-2024-003，商品：平板电脑</div>
                        <div class="filter-list-item-meta">
                            <span>创建时间: 2024-01-15 11:00:00</span>
                            <span>子项目: 6 条</span>
                        </div>
                    </div>
                    
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <span class="filter-list-item-status status-waiting">等待处理</span>
                        <button class="detail-btn" onclick="toggleSubList(this)">详情 ▶</button>
                    </div>
                </div>
                
                <div class="sub-list-container">
                    <div class="sub-list-header">
                        ℹ️ 详细流程信息 (6 条记录)
                    </div>
                    <p style="text-align: center; color: #8c8c8c; padding: 20px;">点击详情按钮查看子列表内容</p>
                </div>
            </div>

        </div>
    </div>

    <script>
        function toggleSubList(button) {
            const listItem = button.closest('.filter-list-item');
            const subList = listItem.querySelector('.sub-list-container');
            const isExpanded = subList.classList.contains('expanded');
            
            if (isExpanded) {
                subList.classList.remove('expanded');
                button.innerHTML = '详情 ▶';
            } else {
                subList.classList.add('expanded');
                button.innerHTML = '详情 ▼';
            }
        }

        // 搜索功能
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const listItems = document.querySelectorAll('.filter-list-item');
            
            listItems.forEach(item => {
                const title = item.querySelector('.filter-list-item-title').textContent.toLowerCase();
                const description = item.querySelector('.filter-list-item-description').textContent.toLowerCase();
                
                if (title.includes(searchTerm) || description.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });

        // 状态筛选功能
        document.getElementById('statusFilter').addEventListener('change', function(e) {
            const selectedStatus = e.target.value;
            const listItems = document.querySelectorAll('.filter-list-item');
            
            listItems.forEach(item => {
                if (selectedStatus === 'all') {
                    item.style.display = 'block';
                } else {
                    const statusElement = item.querySelector('.filter-list-item-status');
                    const itemStatus = statusElement.textContent.trim();
                    
                    if (itemStatus === selectedStatus) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                }
            });
        });
    </script>
</body>
</html>
