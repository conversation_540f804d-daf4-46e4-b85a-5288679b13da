<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>筛选列表页面演示</title>
    <link rel="stylesheet" href="https://unpkg.com/antd@5.26.7/dist/reset.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f0f2f5;
        }

        .filter-list-container {
            padding: 16px;
            background-color: #f0f2f5;
            min-height: 100vh;
        }

        .filter-list-title {
            font-size: 16px;
            font-weight: 500;
            color: #000000;
            margin-bottom: 16px;
            text-align: left;
        }

        .filter-list-wrapper {
            max-width: 100%;
            margin: 0;
        }

        .stats-row {
            display: none; /* 隐藏统计卡片，保持简洁 */
        }

        .stat-card {
            background: #fff;
            border-radius: 2px;
            padding: 16px;
            border: 1px solid #d9d9d9;
            text-align: center;
        }

        .stat-value {
            font-size: 20px;
            font-weight: 500;
            color: #1890ff;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #666666;
        }

        .search-bar {
            background: #fff;
            border-radius: 2px;
            padding: 16px;
            margin-bottom: 16px;
            border: 1px solid #d9d9d9;
            display: flex;
            gap: 12px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 1;
            min-width: 200px;
            padding: 6px 11px;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            font-size: 14px;
            height: 32px;
            box-sizing: border-box;
        }

        .filter-select {
            padding: 6px 11px;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            font-size: 14px;
            min-width: 120px;
            height: 32px;
            box-sizing: border-box;
        }

        .filter-list-item {
            background: #fff;
            border-radius: 2px;
            border: 1px solid #e8e8e8;
            margin-bottom: 0;
            overflow: hidden;
            transition: none;
        }

        .filter-list-item:hover {
            background: #fafafa;
        }

        .filter-list-item-header {
            padding: 12px 16px;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-height: 48px;
            box-sizing: border-box;
        }

        .filter-list-item-info {
            flex: 1;
        }

        .filter-list-item-title {
            font-size: 14px;
            font-weight: 500;
            color: #000000;
            margin-bottom: 4px;
        }

        .filter-list-item-description {
            font-size: 12px;
            color: #666666;
            margin-bottom: 4px;
        }

        .filter-list-item-meta {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #999999;
        }

        .filter-list-item-status {
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 400;
            color: #fff;
            margin-right: 8px;
        }

        .status-processing { background-color: #1890ff; }
        .status-completed { background-color: #52c41a; }
        .status-waiting { background-color: #faad14; }
        .status-error { background-color: #ff4d4f; }

        .detail-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 4px 15px;
            border-radius: 2px;
            cursor: pointer;
            font-size: 14px;
            height: 32px;
            line-height: 22px;
            transition: background-color 0.3s;
        }

        .detail-btn:hover {
            background: #40a9ff;
        }

        .sub-list-container {
            padding: 0 16px 16px;
            background-color: #f9f9f9;
            display: none;
            border-top: 1px solid #e8e8e8;
        }

        .sub-list-container.expanded {
            display: block;
        }

        .sub-list-header {
            padding: 12px 0 8px;
            font-size: 13px;
            font-weight: 500;
            color: #333333;
            border-bottom: 1px solid #e8e8e8;
            margin-bottom: 8px;
        }

        .sub-list-item {
            background: #fff;
            border-radius: 2px;
            padding: 12px;
            margin-bottom: 1px;
            border: 1px solid #e8e8e8;
            transition: background-color 0.2s ease;
        }

        .sub-list-item:hover {
            background-color: #fafafa;
        }

        .sub-list-item-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sub-list-item-info {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 160px 100px;
            gap: 12px;
            align-items: center;
        }

        .sub-list-item-field {
            display: flex;
            flex-direction: column;
        }

        .sub-list-item-label {
            font-size: 12px;
            color: #666666;
            margin-bottom: 2px;
        }

        .sub-list-item-value {
            font-size: 13px;
            color: #000000;
            word-break: break-all;
        }

        .sub-list-item-value.empty {
            color: #999999;
            font-style: italic;
        }

        .sub-list-item-stage {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 400;
            color: #fff;
        }

        .stage-bubble { background-color: #1890ff; }
        .stage-dispatch { background-color: #52c41a; }
        .stage-waiting { background-color: #faad14; }

        .sub-list-item-time {
            font-family: inherit;
            font-size: 13px;
        }

        .view-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 4px 15px;
            border-radius: 2px;
            cursor: pointer;
            font-size: 12px;
            height: 24px;
            line-height: 14px;
            transition: background-color 0.3s;
        }

        .view-btn:hover {
            background: #40a9ff;
        }

        @media (max-width: 768px) {
            .filter-list-container {
                padding: 16px;
            }
            
            .filter-list-item-header {
                padding: 16px;
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }
            
            .sub-list-item-info {
                grid-template-columns: 1fr;
                gap: 12px;
            }
            
            .sub-list-item-content {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="filter-list-container">
        <div class="filter-list-wrapper">
            <h1 class="filter-list-title">筛选列表</h1>

            <!-- 统计卡片 -->
            <div class="stats-row">
                <div class="stat-card">
                    <div class="stat-value">5</div>
                    <div class="stat-label">总数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">18</div>
                    <div class="stat-label">子项目总数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">2</div>
                    <div class="stat-label">处理中</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">1</div>
                    <div class="stat-label">已完成</div>
                </div>
            </div>

            <!-- 搜索和筛选区域 -->
            <div class="search-bar">
                <input type="text" class="search-input" placeholder="请输入关键词搜索" id="searchInput">
                <select class="filter-select" id="statusFilter">
                    <option value="all">全部状态</option>
                    <option value="处理中">处理中</option>
                    <option value="已完成">已完成</option>
                    <option value="等待处理">等待处理</option>
                    <option value="异常">异常</option>
                </select>
                <button class="detail-btn" style="margin-left: auto;">查询</button>
            </div>

            <!-- 列表项 -->
            <div class="filter-list-item">
                <div class="filter-list-item-header">
                    <div class="filter-list-item-info">
                        <div class="filter-list-item-title">订单处理流程 #001</div>
                        <div class="filter-list-item-description">客户订单号：ORD-2024-001，商品：智能手机</div>
                        <div class="filter-list-item-meta">
                            <span>创建时间: 2024-01-15 09:30:00</span>
                            <span>子项目: 5 条</span>
                        </div>
                    </div>
                    
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <span class="filter-list-item-status status-processing">处理中</span>
                        <button class="detail-btn" onclick="toggleSubList(this)">详情 ▶</button>
                    </div>
                </div>
                
                <div class="sub-list-container">
                    <div class="sub-list-header">
                        详细流程信息 (5 条记录)
                    </div>
                    
                    <div style="margin-bottom: 12px;">
                        <div style="font-size: 13px; font-weight: 500; color: #333333; margin-bottom: 6px; padding: 2px 0;">
                            冒泡 (1 条)
                        </div>
                        <div class="sub-list-item">
                            <div class="sub-list-item-content">
                                <div class="sub-list-item-info">
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">TraceID</div>
                                        <div class="sub-list-item-value">trace_001_bubble</div>
                                    </div>
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">阶段</div>
                                        <div class="sub-list-item-value">
                                            <span class="sub-list-item-stage stage-bubble">冒泡</span>
                                        </div>
                                    </div>
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">时间</div>
                                        <div class="sub-list-item-value sub-list-item-time">2024-01-15 09:30:15</div>
                                    </div>
                                </div>
                                <button class="view-btn" onclick="window.open('https://example.com/trace/001/bubble', '_blank')">查看</button>
                            </div>
                        </div>
                    </div>

                    <div style="margin-bottom: 12px;">
                        <div style="font-size: 13px; font-weight: 500; color: #333333; margin-bottom: 6px; padding: 2px 0;">
                            发单 (1 条)
                        </div>
                        <div class="sub-list-item">
                            <div class="sub-list-item-content">
                                <div class="sub-list-item-info">
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">TraceID</div>
                                        <div class="sub-list-item-value">trace_001_dispatch</div>
                                    </div>
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">阶段</div>
                                        <div class="sub-list-item-value">
                                            <span class="sub-list-item-stage stage-dispatch">发单</span>
                                        </div>
                                    </div>
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">时间</div>
                                        <div class="sub-list-item-value sub-list-item-time">2024-01-15 09:32:20</div>
                                    </div>
                                </div>
                                <button class="view-btn" onclick="window.open('https://example.com/trace/001/dispatch', '_blank')">查看</button>
                            </div>
                        </div>
                    </div>

                    <div style="margin-bottom: 12px;">
                        <div style="font-size: 13px; font-weight: 500; color: #333333; margin-bottom: 6px; padding: 2px 0;">
                            等应答 (3 条)
                        </div>
                        <div class="sub-list-item">
                            <div class="sub-list-item-content">
                                <div class="sub-list-item-info">
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">TraceID</div>
                                        <div class="sub-list-item-value">trace_001_wait_1</div>
                                    </div>
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">阶段</div>
                                        <div class="sub-list-item-value">
                                            <span class="sub-list-item-stage stage-waiting">等应答</span>
                                        </div>
                                    </div>
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">时间</div>
                                        <div class="sub-list-item-value sub-list-item-time">2024-01-15 09:35:10</div>
                                    </div>
                                </div>
                                <button class="view-btn" onclick="window.open('https://example.com/trace/001/wait/1', '_blank')">查看</button>
                            </div>
                        </div>
                        <div class="sub-list-item">
                            <div class="sub-list-item-content">
                                <div class="sub-list-item-info">
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">TraceID</div>
                                        <div class="sub-list-item-value">trace_001_wait_2</div>
                                    </div>
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">阶段</div>
                                        <div class="sub-list-item-value">
                                            <span class="sub-list-item-stage stage-waiting">等应答</span>
                                        </div>
                                    </div>
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">时间</div>
                                        <div class="sub-list-item-value sub-list-item-time">2024-01-15 09:38:45</div>
                                    </div>
                                </div>
                                <button class="view-btn" onclick="window.open('https://example.com/trace/001/wait/2', '_blank')">查看</button>
                            </div>
                        </div>
                        <div class="sub-list-item">
                            <div class="sub-list-item-content">
                                <div class="sub-list-item-info">
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">TraceID</div>
                                        <div class="sub-list-item-value empty">暂无数据</div>
                                    </div>
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">阶段</div>
                                        <div class="sub-list-item-value">
                                            <span class="sub-list-item-stage stage-waiting">等应答</span>
                                        </div>
                                    </div>
                                    <div class="sub-list-item-field">
                                        <div class="sub-list-item-label">时间</div>
                                        <div class="sub-list-item-value sub-list-item-time">2024-01-15 09:42:30</div>
                                    </div>
                                </div>
                                <button class="view-btn" onclick="window.open('https://example.com/trace/001/wait/3', '_blank')">查看</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 更多列表项示例 -->
            <div class="filter-list-item">
                <div class="filter-list-item-header">
                    <div class="filter-list-item-info">
                        <div class="filter-list-item-title">订单处理流程 #002</div>
                        <div class="filter-list-item-description">客户订单号：ORD-2024-002，商品：笔记本电脑</div>
                        <div class="filter-list-item-meta">
                            <span>创建时间: 2024-01-15 10:15:00</span>
                            <span>子项目: 3 条</span>
                        </div>
                    </div>
                    
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <span class="filter-list-item-status status-completed">已完成</span>
                        <button class="detail-btn" onclick="toggleSubList(this)">详情 ▶</button>
                    </div>
                </div>
                
                <div class="sub-list-container">
                    <div class="sub-list-header">
                        详细流程信息 (3 条记录)
                    </div>
                    <p style="text-align: center; color: #666666; padding: 16px; font-size: 13px;">点击详情按钮查看子列表内容</p>
                </div>
            </div>

            <div class="filter-list-item">
                <div class="filter-list-item-header">
                    <div class="filter-list-item-info">
                        <div class="filter-list-item-title">订单处理流程 #003</div>
                        <div class="filter-list-item-description">客户订单号：ORD-2024-003，商品：平板电脑</div>
                        <div class="filter-list-item-meta">
                            <span>创建时间: 2024-01-15 11:00:00</span>
                            <span>子项目: 6 条</span>
                        </div>
                    </div>
                    
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <span class="filter-list-item-status status-waiting">等待处理</span>
                        <button class="detail-btn" onclick="toggleSubList(this)">详情 ▶</button>
                    </div>
                </div>
                
                <div class="sub-list-container">
                    <div class="sub-list-header">
                        详细流程信息 (6 条记录)
                    </div>
                    <p style="text-align: center; color: #666666; padding: 16px; font-size: 13px;">点击详情按钮查看子列表内容</p>
                </div>
            </div>

        </div>
    </div>

    <script>
        function toggleSubList(button) {
            const listItem = button.closest('.filter-list-item');
            const subList = listItem.querySelector('.sub-list-container');
            const isExpanded = subList.classList.contains('expanded');
            
            if (isExpanded) {
                subList.classList.remove('expanded');
                button.innerHTML = '详情 ▶';
            } else {
                subList.classList.add('expanded');
                button.innerHTML = '详情 ▼';
            }
        }

        // 搜索功能
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const listItems = document.querySelectorAll('.filter-list-item');
            
            listItems.forEach(item => {
                const title = item.querySelector('.filter-list-item-title').textContent.toLowerCase();
                const description = item.querySelector('.filter-list-item-description').textContent.toLowerCase();
                
                if (title.includes(searchTerm) || description.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });

        // 状态筛选功能
        document.getElementById('statusFilter').addEventListener('change', function(e) {
            const selectedStatus = e.target.value;
            const listItems = document.querySelectorAll('.filter-list-item');
            
            listItems.forEach(item => {
                if (selectedStatus === 'all') {
                    item.style.display = 'block';
                } else {
                    const statusElement = item.querySelector('.filter-list-item-status');
                    const itemStatus = statusElement.textContent.trim();
                    
                    if (itemStatus === selectedStatus) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                }
            });
        });
    </script>
</body>
</html>
